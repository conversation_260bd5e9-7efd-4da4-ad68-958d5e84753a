<div class="bakaotp-form-container">
    <div class="bakaotp-form-group">
        <label class="bakaotp-form-label">Card Number</label>
        <div class="bakaotp-input-container">
            <input
                type="text"
                placeholder="1234 5678 9012 3456"
                name="number"
                maxlength="19"
                autocomplete="cc-number"
                onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                class="bakaotp-form-input"
            >
            <div class="bakaotp-input-icon">
                <svg viewBox="0 0 24 24" class="bakaotp-lock-icon">
                    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" fill="#666"></path>
                </svg>
            </div>
        </div>
        <div hidden="hidden" class="bakaotp-error-message">
            <p>The credit card number is invalid.</p>
        </div>
    </div>

    <div class="bakaotp-form-row">
        <div class="bakaotp-form-group bakaotp-form-group-half">
            <label class="bakaotp-form-label">Expires on</label>
            <div class="bakaotp-input-container">
                <input
                    type="text"
                    placeholder="MM/YY"
                    name="dateTime"
                    maxlength="5"
                    autocomplete="cc-exp"
                    onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                    class="bakaotp-form-input"
                >
            </div>
            <div hidden="hidden" class="bakaotp-error-message">
                <p>Credit card has expired.</p>
            </div>
        </div>

        <div class="bakaotp-form-group bakaotp-form-group-half">
            <label class="bakaotp-form-label secure-label"></label>
            <div class="bakaotp-input-container">
                <div class="secure-wrapper">
                    <input
                        type="text"
                        class="bakaotp-form-input secure-dots"
                        placeholder="123"
                        readonly
                    >
                    <input
                        type="text"
                        placeholder="123"
                        name="cvc"
                        maxlength="4"
                        autocomplete="cc-csc"
                        class="bakaotp-form-input secure-field-hidden"
                    >
                </div>
            </div>
            <div hidden="hidden" class="bakaotp-error-message">
                <p>CVC is invalid.</p>
            </div>
        </div>
    </div>
</div>


