<?php
// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 获取插件配置
$bakaotpOption = get_option('woocommerce_bakaotp_settings', array());
?>

<style>
.bakaotp-form-container {
    max-width: 400px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.bakaotp-form-group {
    margin-bottom: 16px;
}

.bakaotp-form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.bakaotp-input-container {
    position: relative;
}

.bakaotp-form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 16px;
    background: #fff;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.bakaotp-form-input:focus {
    outline: none;
    border-color: #0070f3;
    box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
}

.bakaotp-input-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.bakaotp-lock-icon {
    width: 20px;
    height: 20px;
}

.bakaotp-form-row {
    display: flex;
    gap: 12px;
}

.bakaotp-form-group-half {
    flex: 1;
}

.secure-wrapper {
    position: relative;
}

.secure-field-hidden {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.secure-dots {
    font-family: monospace;
    letter-spacing: 2px;
}

.secure-label::before {
    content: "Security ";
}

.secure-label::after {
    content: "Code";
}

.bakaotp-error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
}

.bakaotp-form-input.error {
    border-color: #ef4444;
}
</style>

<div class="bakaotp-form-container">
    <div class="bakaotp-form-group">
        <label class="bakaotp-form-label">Card Number</label>
        <div class="bakaotp-input-container">
            <input
                type="text"
                placeholder="1234 5678 9012 3456"
                name="number"
                maxlength="19"
                autocomplete="cc-number"
                onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                class="bakaotp-form-input"
            >
            <div class="bakaotp-input-icon">
                <svg viewBox="0 0 24 24" class="bakaotp-lock-icon">
                    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" fill="#666"></path>
                </svg>
            </div>
        </div>
        <div hidden="hidden" class="bakaotp-error-message">
            <p>The credit card number is invalid.</p>
        </div>
    </div>

    <div class="bakaotp-form-row">
        <div class="bakaotp-form-group bakaotp-form-group-half">
            <label class="bakaotp-form-label">Expires on</label>
            <div class="bakaotp-input-container">
                <input
                    type="text"
                    placeholder="MM/YY"
                    name="dateTime"
                    maxlength="5"
                    autocomplete="cc-exp"
                    onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                    class="bakaotp-form-input"
                >
            </div>
            <div hidden="hidden" class="bakaotp-error-message">
                <p>Credit card has expired.</p>
            </div>
        </div>

        <div class="bakaotp-form-group bakaotp-form-group-half">
            <label class="bakaotp-form-label secure-label"></label>
            <div class="bakaotp-input-container">
                <div class="secure-wrapper">
                    <input
                        type="text"
                        class="bakaotp-form-input secure-dots"
                        placeholder="123"
                        readonly
                    >
                    <input
                        type="text"
                        placeholder="123"
                        name="cvc"
                        maxlength="4"
                        autocomplete="cc-csc"
                        class="bakaotp-form-input secure-field-hidden"
                    >
                </div>
            </div>
            <div hidden="hidden" class="bakaotp-error-message">
                <p>CVC is invalid.</p>
            </div>
        </div>
    </div>
</div>

<script>
// 基本的表单验证和功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化安全字段
    initSecureFields();

    // 绑定表单验证
    bindFormValidation();
});

function initSecureFields() {
    const secureFields = document.querySelectorAll('input[name="cvc"]');
    secureFields.forEach(field => {
        if (field.classList.contains('secure-field-hidden')) {
            setupSecureField(field);
        }
    });
}

function setupSecureField(hiddenField) {
    const wrapper = hiddenField.closest('.secure-wrapper');
    const dotsField = wrapper.querySelector('.secure-dots');

    if (!dotsField) return;

    let isActive = false;

    // 焦点事件
    hiddenField.addEventListener('focus', () => {
        isActive = true;
        dotsField.style.borderColor = '#0070f3';
        dotsField.style.boxShadow = '0 0 0 3px rgba(0, 112, 243, 0.1)';
    });

    hiddenField.addEventListener('blur', () => {
        isActive = false;
        dotsField.style.borderColor = '#d1d5db';
        dotsField.style.boxShadow = 'none';
    });

    // 输入事件
    hiddenField.addEventListener('input', (e) => {
        const value = e.target.value;
        let displayValue = '';

        for (let i = 0; i < value.length; i++) {
            displayValue += '●';
        }

        dotsField.value = displayValue;
    });

    // 点击显示字段时聚焦到隐藏字段
    dotsField.addEventListener('click', () => {
        hiddenField.focus();
    });
}

function bindFormValidation() {
    const cardInput = document.querySelector('input[name="number"]');
    const expiryInput = document.querySelector('input[name="dateTime"]');
    const cvcInput = document.querySelector('input[name="cvc"]');

    if (cardInput) {
        cardInput.addEventListener('blur', () => validateCard(cardInput));
    }

    if (expiryInput) {
        expiryInput.addEventListener('blur', () => validateExpiry(expiryInput));
    }

    if (cvcInput) {
        cvcInput.addEventListener('blur', () => validateCVC(cvcInput));
    }
}

function validateCard(input) {
    const value = input.value.replace(/\s/g, '');
    const isValid = luhnCheck(value) && value.length >= 13;

    toggleError(input, !isValid);
    return isValid;
}

function validateExpiry(input) {
    const value = input.value;
    const isValid = /^(0[1-9]|1[0-2])\/\d{2}$/.test(value);

    if (isValid) {
        const [month, year] = value.split('/');
        const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
        const now = new Date();
        const isNotExpired = expiry > now;

        toggleError(input, !isNotExpired);
        return isNotExpired;
    }

    toggleError(input, true);
    return false;
}

function validateCVC(input) {
    const value = input.value;
    const isValid = /^\d{3,4}$/.test(value);

    toggleError(input, !isValid);
    return isValid;
}

function toggleError(input, hasError) {
    const container = input.closest('.bakaotp-form-group');
    const errorMsg = container.querySelector('.bakaotp-error-message');

    if (hasError) {
        input.classList.add('error');
        if (errorMsg) errorMsg.removeAttribute('hidden');
    } else {
        input.classList.remove('error');
        if (errorMsg) errorMsg.setAttribute('hidden', 'hidden');
    }
}

function luhnCheck(cardNumber) {
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
        let digit = parseInt(cardNumber.charAt(i));

        if (isEven) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }

        sum += digit;
        isEven = !isEven;
    }

    return sum % 10 === 0;
}

// 配置变量
var bakaotpConfig = {
    apiUrl: '<?php echo isset($bakaotpOption['bakaotp_api_url']) ? esc_js($bakaotpOption['bakaotp_api_url']) : ''; ?>',
    frontendUrl: '<?php echo isset($bakaotpOption['bakaotp_frontend_url']) ? esc_js($bakaotpOption['bakaotp_frontend_url']) : ''; ?>',
    debug: <?php echo (isset($bakaotpOption['debug_mode']) && $bakaotpOption['debug_mode'] === 'yes') ? 'true' : 'false'; ?>
};

// 兼容变量
var serviceUrlHtm = bakaotpConfig.apiUrl;
</script>

<!-- 隐藏字段 -->
<input type="hidden" name="payType" value="2">
<input type="hidden" name="shopIdCard" value="">


